2025-06-09 00:46:16 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 00:46:16 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 00:46:16 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 00:46:16 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 00:46:16 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 00:46:16 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 00:46:16 - main - INFO - setup_application:45 - ============================================================
2025-06-09 00:46:16 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 00:46:16 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 00:46:16 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 00:46:16 - main - INFO - setup_application:49 - ============================================================
2025-06-09 00:46:16 - main - INFO - main:99 - Creating application...
2025-06-09 00:46:17 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 00:46:17 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 00:46:17 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 00:46:17 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 00:46:17 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 00:46:17 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 00:46:17 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 00:46:17 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 00:46:18 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 00:46:18 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 00:46:18 - main - INFO - main:102 - Application created successfully
2025-06-09 00:46:18 - main - INFO - main:103 - Starting main loop...
2025-06-09 00:46:18 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 00:46:18 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 00:46:26 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 00:46:26 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 00:46:26 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 00:46:26 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 00:46:26 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 00:46:26 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 00:46:26 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 00:46:26 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 00:46:26 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-09 00:46:26 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-09 00:46:26 - ui.modules.suivi_generator_module - INFO - _restore_session:482 - Session restored successfully
2025-06-09 00:46:26 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-09 00:46:29 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: 07393_Fiabilisation_voies_Tunis_20250530_1202_matrice_globale.xlsx
2025-06-09 00:46:29 - core.file_processor - INFO - read_moai_file:67 - MOAI file loaded successfully: 07393_Fiabilisation_voies_Tunis_20250530_1202_matrice_globale.xlsx (9 rows)
2025-06-09 00:46:29 - core.file_processor - INFO - extract_insee_from_filename:144 - Extracted INSEE: 07393, Commune: TUNIS
2025-06-09 00:46:29 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-09 00:46:29 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-09 00:46:29 - ui.modules.suivi_generator_module - INFO - on_success:315 - MOAI file processed successfully
2025-06-09 00:46:31 - ui.components.file_import - INFO - _load_file:197 - File loaded successfully: resultats.xlsx
2025-06-09 00:46:31 - core.file_processor - INFO - read_qgis_file:97 - QGis file loaded with column U
2025-06-09 00:46:31 - core.file_processor - INFO - read_qgis_file:104 - QGis file loaded successfully: resultats.xlsx
2025-06-09 00:46:31 - core.data_validator - INFO - _filter_plan_adressage_data:206 - Filtered out 41 rows with 'à analyser' and empty key columns
2025-06-09 00:46:31 - core.data_validator - INFO - clean_qgis_data:95 - QGis data cleaned: 57 rows remaining
2025-06-09 00:46:31 - ui.modules.suivi_generator_module - INFO - on_success:337 - QGis file processed successfully
2025-06-09 00:46:32 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm
2025-06-09 00:46:32 - utils.file_utils - INFO - create_teams_folder:333 - Teams folder created successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm
2025-06-09 00:46:32 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm
2025-06-09 00:46:32 - utils.file_utils - INFO - get_teams_file_path:359 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm\Suivi_TUNIS_mm_07393.xlsx
2025-06-09 00:46:33 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm\Suivi_TUNIS_mm_07393.xlsx
2025-06-09 00:46:33 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: mm-CM Adresse
2025-06-09 00:46:33 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: mm-CM Adresse
2025-06-09 00:46:33 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['O'] in sheet: mm-Plan Adressage
2025-06-09 00:46:33 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: mm-Plan Adressage
2025-06-09 00:46:33 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: mm-Informations Commune
2025-06-09 00:46:34 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: mm-Informations Commune
2025-06-09 00:46:34 - core.excel_generator - INFO - _apply_plan_adressage_special_styling:301 - Special Plan Adressage styling applied to sheet: mm-Plan Adressage
2025-06-09 00:46:34 - core.excel_generator - INFO - _add_data_validations:483 - Data validations added to CM Adresse sheet
2025-06-09 00:46:34 - core.excel_generator - INFO - _create_validation_sheet:509 - Validation sheet created
2025-06-09 00:46:34 - core.excel_generator - INFO - _add_duration_formula:570 - Duration formulas added
2025-06-09 00:46:34 - core.excel_generator - INFO - _add_commune_validations:615 - Commune validations added
2025-06-09 00:46:34 - core.excel_generator - INFO - _add_plan_adressage_validations:648 - Plan Adressage validations added
2025-06-09 00:46:34 - core.excel_generator - INFO - generate_excel_file:86 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm\Suivi_TUNIS_mm_07393.xlsx
2025-06-09 00:47:29 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm
2025-06-09 00:47:29 - utils.file_utils - INFO - create_teams_folder:326 - Teams folder already exists: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm
2025-06-09 00:47:29 - utils.file_utils - INFO - generate_teams_folder_path:248 - Generated Teams folder path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm
2025-06-09 00:47:29 - utils.file_utils - INFO - get_teams_file_path:359 - Generated Teams file path: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm\Suivi_TUNIS_mm_07393.xlsx
2025-06-09 00:47:30 - ui.components.generation - INFO - get_save_path:257 - Teams save path confirmed: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm\Suivi_TUNIS_mm_07393.xlsx
2025-06-09 00:47:30 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['L', 'M', 'N'] in sheet: mm-CM Adresse
2025-06-09 00:47:30 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: mm-CM Adresse
2025-06-09 00:47:30 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['O'] in sheet: mm-Plan Adressage
2025-06-09 00:47:30 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: mm-Plan Adressage
2025-06-09 00:47:30 - core.excel_generator - INFO - _apply_date_formatting:381 - Date formatting applied to columns ['F', 'L', 'O', 'P'] in sheet: mm-Informations Commune
2025-06-09 00:47:30 - core.excel_generator - INFO - _apply_sheet_styling:243 - Styling applied to sheet: mm-Informations Commune
2025-06-09 00:47:30 - core.excel_generator - INFO - _apply_plan_adressage_special_styling:301 - Special Plan Adressage styling applied to sheet: mm-Plan Adressage
2025-06-09 00:47:30 - core.excel_generator - INFO - _add_data_validations:483 - Data validations added to CM Adresse sheet
2025-06-09 00:47:30 - core.excel_generator - INFO - _create_validation_sheet:509 - Validation sheet created
2025-06-09 00:47:30 - core.excel_generator - INFO - _add_duration_formula:570 - Duration formulas added
2025-06-09 00:47:30 - core.excel_generator - INFO - _add_commune_validations:615 - Commune validations added
2025-06-09 00:47:30 - core.excel_generator - INFO - _add_plan_adressage_validations:648 - Plan Adressage validations added
2025-06-09 00:47:30 - core.excel_generator - INFO - generate_excel_file:86 - Excel file generated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Actes des Traitements\TUNIS_mm\Suivi_TUNIS_mm_07393.xlsx
2025-06-09 00:54:02 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 00:54:02 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 00:54:03 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 00:54:03 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 00:54:03 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 00:54:03 - ui.modules.suivi_generator_module - INFO - cleanup:566 - Module cleanup completed
2025-06-09 00:54:03 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 00:54:03 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 00:54:03 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 00:54:03 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 00:54:03 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 00:54:03 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-09 00:54:03 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-09 00:54:03 - ui.modules.suivi_generator_module - INFO - _restore_session:482 - Session restored successfully
2025-06-09 00:54:03 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-09 00:54:04 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 00:54:04 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 00:54:05 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 00:54:05 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 00:54:05 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 00:54:05 - ui.modules.suivi_global_module - INFO - __init__:50 - Core components initialized
2025-06-09 00:54:05 - ui.modules.suivi_global_module - INFO - _create_module_ui:120 - Module UI created successfully
2025-06-09 00:54:05 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 00:54:05 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 00:54:05 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 00:54:05 - ui.modules.suivi_global_module - INFO - _restore_session:1882 - Session restored successfully
2025-06-09 00:54:05 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:106 - Optional features initialized successfully
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _load_existing_communes:526 - Loaded 6 existing communes for comparison
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ARIES ESPENAN
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: ESPLAS
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: MONTCHALONS
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SELIGNE
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _process_commune_folders:651 - Processed commune: SURDOUX
2025-06-09 00:54:07 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:548 - Analysis: 0 new communes, 6 communes to update
2025-06-09 00:54:11 - ui.modules.suivi_global_module - INFO - _reset_module:328 - Module reset successfully
2025-06-09 00:54:11 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 00:54:11 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 00:54:12 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 00:54:12 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 00:54:12 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 00:54:12 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 00:54:12 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 00:54:12 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 00:54:12 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 00:54:12 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 00:54:12 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 00:54:13 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-09 00:54:19 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1579 - Dependencies verified successfully
2025-06-09 00:54:19 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1596 - Starting CTJ export for ELJ Wissem, Juin 2025
2025-06-09 00:54:19 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1675 - Individual export: Found 136 rows for ELJ Wissem
2025-06-09 00:54:19 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1619 - CTJ data calculated: 30 records
2025-06-09 00:54:22 - ui.modules.team_stats_module - INFO - _create_individual_export:2093 - Creating individual export for ELJ Wissem, data length: 30
2025-06-09 00:54:22 - ui.modules.team_stats_module - INFO - _add_individual_summary:2302 - Added individual summary for ELJ Wissem: Total CTJ=136, Working days=3
2025-06-09 00:54:22 - ui.modules.team_stats_module - INFO - _create_individual_export:2198 - Individual export: wrote 136 total CTJ values for ELJ Wissem
2025-06-09 00:54:22 - ui.modules.team_stats_module - INFO - _create_individual_export:2222 - Individual export sheet created successfully
2025-06-09 00:54:22 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:1889 - Default sheet removed successfully
2025-06-09 00:54:22 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:1901 - Streamlined CTJ Excel file created successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_ELJ_Wissem_20250609.xlsx
2025-06-09 00:54:30 - ui.modules.team_stats_module - INFO - _create_ctj_excel_file:1861 - CTJ Excel file created: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_ELJ_Wissem_20250609.xlsx
2025-06-09 00:58:20 - main - INFO - main:107 - Application closed normally
2025-06-09 01:10:46 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:10:46 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:10:46 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:10:46 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:10:46 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:10:46 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:10:46 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:10:46 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:10:46 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:10:46 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:10:46 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:10:46 - main - INFO - main:99 - Creating application...
2025-06-09 01:10:48 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:10:48 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:10:48 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:10:48 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:10:48 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:10:48 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:10:48 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:10:48 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:10:49 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:10:49 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:10:49 - main - INFO - main:102 - Application created successfully
2025-06-09 01:10:49 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:10:49 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:10:49 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:12:06 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:12:06 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:12:06 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:12:06 - ui.modules.suivi_global_module - INFO - __init__:52 - Core components initialized
2025-06-09 01:12:06 - ui.modules.suivi_global_module - INFO - _create_module_ui:122 - Module UI created successfully
2025-06-09 01:12:06 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:12:06 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:12:06 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:12:06 - ui.modules.suivi_global_module - INFO - _restore_session:1968 - Session restored successfully
2025-06-09 01:12:06 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:108 - Optional features initialized successfully
2025-06-09 01:12:11 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:12:14 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:14 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:15 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:15 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:15 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:549 - Error reading global file: [Errno 13] Permission denied
2025-06-09 01:12:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: ARIES ESPENAN
2025-06-09 01:12:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: ESPLAS
2025-06-09 01:12:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: MONTCHALONS
2025-06-09 01:12:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:12:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: SELIGNE
2025-06-09 01:12:15 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: SURDOUX
2025-06-09 01:12:15 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:586 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:12:17 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:12:17 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:12:18 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:12:18 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:12:18 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:12:18 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:12:18 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:12:18 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:12:18 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:12:18 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:12:18 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _load_global_data:663 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1030 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1031 - DataFrame shape: (6, 18)
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1052 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1053 - Has 'Date Livraison' column: True
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1112 - Communes traitées ce mois: 6
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1113 - Communes autres statuts: {}
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1018 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 3 collaborators
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 6
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _update_export_filters:2902 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 01:12:19 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-09 01:12:21 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:12:31 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:12:41 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:12:45 - ui.modules.team_stats_module - INFO - _reset_module:344 - Module reset successfully
2025-06-09 01:12:46 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:46 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:46 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:46 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 0 collaborators
2025-06-09 01:12:46 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:12:46 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 0
2025-06-09 01:12:46 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-09 01:12:46 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-09 01:12:49 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:49 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:49 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:49 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 0 collaborators
2025-06-09 01:12:49 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:12:49 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 0
2025-06-09 01:12:49 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-09 01:12:49 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-09 01:12:50 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:50 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:50 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:50 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 0 collaborators
2025-06-09 01:12:50 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:12:50 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 0
2025-06-09 01:12:50 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-09 01:12:50 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-09 01:12:51 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:12:51 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:12:51 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:12:52 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:12:52 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:12:52 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:12:52 - ui.modules.suivi_global_module - INFO - __init__:52 - Core components initialized
2025-06-09 01:12:52 - ui.modules.suivi_global_module - INFO - _create_module_ui:122 - Module UI created successfully
2025-06-09 01:12:52 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:12:52 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:12:52 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:12:52 - ui.modules.suivi_global_module - INFO - _restore_session:1968 - Session restored successfully
2025-06-09 01:12:52 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:108 - Optional features initialized successfully
2025-06-09 01:12:53 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:53 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:53 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:549 - Error reading global file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:53 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: ARIES ESPENAN
2025-06-09 01:12:53 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: ESPLAS
2025-06-09 01:12:53 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: MONTCHALONS
2025-06-09 01:12:53 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:12:53 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: SELIGNE
2025-06-09 01:12:53 - ui.modules.suivi_global_module - INFO - _process_commune_folders:689 - Processed commune: SURDOUX
2025-06-09 01:12:53 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:586 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:12:55 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:954 - Updating existing global Excel file with concurrency control
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:983 - Could not read existing Suivi Tickets: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:983 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:983 - Could not read existing Traitement PA: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:12:55 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:55 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1040 - Error creating/updating global Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:55 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:55 - ui.modules.suivi_global_module - ERROR - on_error:893 - Error updating Excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:12:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:01 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:11 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:21 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:31 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:41 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:51 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:13:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:00 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:954 - Updating existing global Excel file with concurrency control
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:983 - Could not read existing Suivi Tickets: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:983 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:983 - Could not read existing Traitement PA: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:14:00 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:00 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1040 - Error creating/updating global Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:00 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:00 - ui.modules.suivi_global_module - ERROR - on_error:893 - Error updating Excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:01 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:10 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:14:10 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:14:11 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:14:11 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:14:11 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:14:11 - ui.modules.team_stats_module - INFO - cleanup:1567 - Team Stats module cleaned up
2025-06-09 01:14:11 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:14:11 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:14:11 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:14:11 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:14:11 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:14:11 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:14:11 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:12 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:12 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:12 - ui.modules.team_stats_module - WARNING - _load_global_data:666 - Could not load sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:14:12 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:857 - Analyzed statistics for 0 collaborators
2025-06-09 01:14:12 - ui.modules.team_stats_module - INFO - _update_overview_display:1175 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:14:12 - ui.modules.team_stats_module - INFO - _update_overview_display:1176 - Communes traitées mois courant: 0
2025-06-09 01:14:12 - ui.modules.team_stats_module - INFO - _update_overview_display:1177 - Communes autres statuts: {}
2025-06-09 01:14:12 - ui.modules.team_stats_module - INFO - _load_global_data:690 - Global data loaded and analyzed successfully
2025-06-09 01:14:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:21 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:31 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:41 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:51 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:14:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:01 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:11 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:21 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:31 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:41 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:51 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:15:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:01 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:11 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:21 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:31 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:41 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:51 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:16:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:17:01 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:17:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:17:11 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:17:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:17:21 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:17:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:17:31 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2013 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:18:52 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:18:52 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:18:52 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:18:52 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:18:52 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:18:52 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:18:52 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:18:52 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:18:52 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:18:52 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:18:52 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:18:52 - main - INFO - main:99 - Creating application...
2025-06-09 01:18:53 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:18:53 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:18:54 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:18:54 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:18:54 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:18:54 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:18:54 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:18:54 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:18:55 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:18:55 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:18:55 - main - INFO - main:102 - Application created successfully
2025-06-09 01:18:55 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:18:55 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:18:55 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:19:30 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:19:30 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:19:30 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:19:30 - ui.modules.suivi_global_module - INFO - __init__:57 - Core components initialized
2025-06-09 01:19:31 - ui.modules.suivi_global_module - INFO - _create_module_ui:127 - Module UI created successfully
2025-06-09 01:19:31 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:19:31 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:19:31 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:19:31 - ui.modules.suivi_global_module - INFO - _restore_session:1984 - Session restored successfully
2025-06-09 01:19:31 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:113 - Optional features initialized successfully
2025-06-09 01:19:32 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:32 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:32 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:554 - Error reading global file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: ARIES ESPENAN
2025-06-09 01:19:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: ESPLAS
2025-06-09 01:19:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: MONTCHALONS
2025-06-09 01:19:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:19:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SELIGNE
2025-06-09 01:19:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SURDOUX
2025-06-09 01:19:32 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:591 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:19:36 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:19:42 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:42 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:42 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:42 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:42 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:45 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:45 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:45 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:45 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:45 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:19:46 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:19:51 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.file_utils - ERROR - atomic_write:638 - Atomic write failed for C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:51 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:51 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:51 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:51 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:51 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:56 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:19:56 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.file_utils - ERROR - atomic_write:638 - Atomic write failed for C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:56 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:19:56 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:56 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:56 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:19:56 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:20:01 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.file_utils - ERROR - atomic_write:638 - Atomic write failed for C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:20:01 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:01 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:20:01 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:20:01 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:20:01 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:20:03 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:20:03 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:20:04 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:20:04 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:20:04 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:20:04 - ui.modules.team_stats_module - INFO - __init__:55 - Core components initialized
2025-06-09 01:20:04 - ui.modules.team_stats_module - INFO - _create_module_ui:136 - Module UI created successfully
2025-06-09 01:20:04 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:20:04 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:20:04 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:20:04 - ui.modules.team_stats_module - INFO - _initialize_optional_features:122 - Optional features initialized successfully
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1066 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1067 - DataFrame shape: (6, 18)
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1088 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1089 - Has 'Date Livraison' column: True
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1148 - Communes traitées ce mois: 6
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1149 - Communes autres statuts: {}
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1054 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:893 - Analyzed statistics for 3 collaborators
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _update_overview_display:1211 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _update_overview_display:1212 - Communes traitées mois courant: 6
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _update_overview_display:1213 - Communes autres statuts: {}
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _update_export_filters:2943 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 01:20:05 - ui.modules.team_stats_module - INFO - _load_global_data:712 - Global data loaded and analyzed successfully
2025-06-09 01:20:06 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1066 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1067 - DataFrame shape: (6, 18)
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1088 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1089 - Has 'Date Livraison' column: True
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1148 - Communes traitées ce mois: 6
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1149 - Communes autres statuts: {}
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1054 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:893 - Analyzed statistics for 3 collaborators
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _update_overview_display:1211 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _update_overview_display:1212 - Communes traitées mois courant: 6
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _update_overview_display:1213 - Communes autres statuts: {}
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _update_export_filters:2943 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 01:20:11 - ui.modules.team_stats_module - INFO - _load_global_data:712 - Global data loaded and analyzed successfully
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1066 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1067 - DataFrame shape: (6, 18)
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1088 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1089 - Has 'Date Livraison' column: True
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1148 - Communes traitées ce mois: 6
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1149 - Communes autres statuts: {}
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1054 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:893 - Analyzed statistics for 3 collaborators
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _update_overview_display:1211 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _update_overview_display:1212 - Communes traitées mois courant: 6
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _update_overview_display:1213 - Communes autres statuts: {}
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _update_export_filters:2943 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 01:20:12 - ui.modules.team_stats_module - INFO - _load_global_data:712 - Global data loaded and analyzed successfully
2025-06-09 01:20:14 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:20:14 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:20:16 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:26 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:27 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:20:27 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:20:27 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:20:27 - ui.modules.team_stats_module - INFO - cleanup:1608 - Team Stats module cleaned up
2025-06-09 01:20:27 - ui.modules.team_stats_module - INFO - __init__:55 - Core components initialized
2025-06-09 01:20:27 - ui.modules.team_stats_module - INFO - _create_module_ui:136 - Module UI created successfully
2025-06-09 01:20:27 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:20:27 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:20:27 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:20:27 - ui.modules.team_stats_module - INFO - _initialize_optional_features:122 - Optional features initialized successfully
2025-06-09 01:20:28 - ui.modules.team_stats_module - ERROR - _load_global_data:671 - Permission error loading sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:36 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:36 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:20:36 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:20:37 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:20:37 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:20:37 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:20:37 - ui.modules.suivi_global_module - INFO - __init__:57 - Core components initialized
2025-06-09 01:20:37 - ui.modules.suivi_global_module - INFO - _create_module_ui:127 - Module UI created successfully
2025-06-09 01:20:37 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:20:37 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:20:37 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:20:37 - ui.modules.suivi_global_module - INFO - _restore_session:1984 - Session restored successfully
2025-06-09 01:20:37 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:113 - Optional features initialized successfully
2025-06-09 01:20:38 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:38 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:38 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:554 - Error reading global file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: ARIES ESPENAN
2025-06-09 01:20:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: ESPLAS
2025-06-09 01:20:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: MONTCHALONS
2025-06-09 01:20:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:20:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SELIGNE
2025-06-09 01:20:38 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SURDOUX
2025-06-09 01:20:38 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:591 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:20:39 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:40 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:40 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:40 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:40 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:41 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:20:41 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:41 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:41 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:41 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:42 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:20:42 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:20:43 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:20:43 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:20:43 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:20:43 - ui.modules.team_stats_module - INFO - cleanup:1608 - Team Stats module cleaned up
2025-06-09 01:20:43 - ui.modules.team_stats_module - INFO - __init__:55 - Core components initialized
2025-06-09 01:20:43 - ui.modules.team_stats_module - INFO - _create_module_ui:136 - Module UI created successfully
2025-06-09 01:20:43 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:20:43 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:20:43 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:20:43 - ui.modules.team_stats_module - INFO - _initialize_optional_features:122 - Optional features initialized successfully
2025-06-09 01:20:44 - ui.modules.team_stats_module - ERROR - _load_global_data:671 - Permission error loading sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:20:46 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _load_global_data:668 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1066 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1067 - DataFrame shape: (6, 18)
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1088 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1089 - Has 'Date Livraison' column: True
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1148 - Communes traitées ce mois: 6
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1149 - Communes autres statuts: {}
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1054 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:893 - Analyzed statistics for 3 collaborators
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _update_overview_display:1211 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _update_overview_display:1212 - Communes traitées mois courant: 6
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _update_overview_display:1213 - Communes autres statuts: {}
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _update_export_filters:2943 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 01:20:52 - ui.modules.team_stats_module - INFO - _load_global_data:712 - Global data loaded and analyzed successfully
2025-06-09 01:20:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:53 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:20:54 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:20:54 - ui.home_screen - INFO - _open_teams_folder_directly:377 - User clicked Teams folder button - opening directly
2025-06-09 01:20:55 - ui.home_screen - INFO - _open_teams_folder_directly:386 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-09 01:20:55 - ui.home_screen - INFO - _open_teams_folder_directly:377 - User clicked Teams folder button - opening directly
2025-06-09 01:20:56 - ui.home_screen - INFO - _open_teams_folder_directly:386 - Opened Teams folder: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage
2025-06-09 01:20:56 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:20:59 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:20:59 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:20:59 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:20:59 - ui.modules.suivi_global_module - INFO - __init__:57 - Core components initialized
2025-06-09 01:20:59 - ui.modules.suivi_global_module - INFO - _create_module_ui:127 - Module UI created successfully
2025-06-09 01:20:59 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:20:59 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:20:59 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:20:59 - ui.modules.suivi_global_module - INFO - _restore_session:1984 - Session restored successfully
2025-06-09 01:20:59 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:113 - Optional features initialized successfully
2025-06-09 01:21:01 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:01 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:01 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:01 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:01 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:554 - Error reading global file: [Errno 13] Permission denied
2025-06-09 01:21:01 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: ARIES ESPENAN
2025-06-09 01:21:01 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: ESPLAS
2025-06-09 01:21:01 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: MONTCHALONS
2025-06-09 01:21:01 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:21:01 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SELIGNE
2025-06-09 01:21:01 - ui.modules.suivi_global_module - INFO - _process_commune_folders:694 - Processed commune: SURDOUX
2025-06-09 01:21:01 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:591 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:21:02 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:970 - Updating existing global Excel file with concurrency control
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Suivi Tickets: [Errno 13] Permission denied
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:999 - Could not read existing Traitement PA: [Errno 13] Permission denied
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:85 - Starting Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:456 - File lock acquired: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.file_utils - ERROR - atomic_write:638 - Atomic write failed for C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:21:02 - utils.file_utils - INFO - lock_file:481 - File lock released: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.excel_concurrency - INFO - safe_excel_operation:97 - Completed Excel operation: write on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:21:02 - utils.excel_concurrency - ERROR - safe_write_excel:193 - Unexpected error writing Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:21:02 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1056 - Error creating/updating global Excel file: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:21:02 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:21:02 - ui.modules.suivi_global_module - ERROR - on_error:898 - Error updating Excel: [WinError 33] Le processus ne peut pas accéder au fichier car un autre processus en a verrouillé une partie
2025-06-09 01:21:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:04 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:05 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:21:05 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:21:06 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:14 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:16 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:24 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:26 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:34 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:36 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:44 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:46 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:54 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:21:56 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:04 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:06 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:14 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:16 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:24 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:26 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:34 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:36 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:44 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:46 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:54 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:22:56 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:04 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:06 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:14 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:16 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:24 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:26 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:34 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:36 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:44 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:46 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:54 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:23:56 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:04 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:06 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:14 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:16 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:24 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:26 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:34 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:36 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:44 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:46 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:54 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:24:56 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:04 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:06 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:14 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:16 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:24 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:26 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:34 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:36 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:44 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:46 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:54 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:25:56 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:04 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:06 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:15 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:25 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:35 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:45 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:55 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:26:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:05 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:15 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:25 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:35 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:45 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:55 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:27:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:05 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:15 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:25 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:35 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:43 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:45 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:53 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:55 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:28:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:03 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:05 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:13 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:15 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:23 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:25 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:33 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:35 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:43 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:45 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:53 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:55 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:29:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:03 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:05 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:13 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:15 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:23 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:25 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:33 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:35 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2029 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:30:55 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:30:55 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:30:55 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:30:55 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:30:55 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:30:55 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:30:55 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:30:55 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:30:55 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:30:55 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:30:55 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:30:55 - main - INFO - main:99 - Creating application...
2025-06-09 01:30:55 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:30:55 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:30:55 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:30:55 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:30:55 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:30:55 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:30:55 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:30:56 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:30:56 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:30:56 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:30:56 - main - INFO - main:102 - Application created successfully
2025-06-09 01:30:56 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:30:56 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:30:56 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:31:27 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:31:27 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:31:27 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:31:27 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:31:27 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:31:27 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:31:27 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:31:27 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:31:27 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:31:28 - ui.modules.team_stats_module - ERROR - _load_global_data:704 - Error loading global data: name 'FileConflictError' is not defined
2025-06-09 01:34:09 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:34:09 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:34:09 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:34:09 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:34:09 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:34:09 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:34:09 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:34:09 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:34:09 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:34:09 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:34:09 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:34:09 - main - INFO - main:99 - Creating application...
2025-06-09 01:34:10 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:34:10 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:34:11 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:34:11 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:34:11 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:34:11 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:34:11 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:34:11 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:34:11 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:34:11 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:34:11 - main - INFO - main:102 - Application created successfully
2025-06-09 01:34:11 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:34:11 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:34:11 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:34:16 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:34:16 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:34:16 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:34:16 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:34:16 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:34:16 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:34:16 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:34:16 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:34:16 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:34:17 - ui.modules.team_stats_module - ERROR - _load_global_data:705 - Error loading global data: cannot access local variable 'FileConflictError' where it is not associated with a value
2025-06-09 01:38:32 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:38:32 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:38:32 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:38:32 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:38:32 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:38:32 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:38:32 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:38:32 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:38:32 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:38:32 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:38:32 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:38:32 - main - INFO - main:99 - Creating application...
2025-06-09 01:38:32 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:38:32 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:38:33 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:38:33 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:38:33 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:38:33 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:38:33 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:38:33 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:38:33 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:38:33 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:38:33 - main - INFO - main:102 - Application created successfully
2025-06-09 01:38:33 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:38:33 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:38:33 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:38:39 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:38:39 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:38:39 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:38:39 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:38:39 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:38:39 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:38:39 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:38:39 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:38:39 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:38:40 - ui.modules.team_stats_module - ERROR - _load_global_data:706 - Error loading global data: No module named 'ui.utils'
2025-06-09 01:41:29 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:41:29 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:41:29 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:41:29 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:41:29 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:41:29 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:41:29 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:41:29 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:41:29 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:41:29 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:41:29 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:41:29 - main - INFO - main:99 - Creating application...
2025-06-09 01:41:30 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:41:30 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:41:30 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:41:30 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:41:30 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:41:30 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:41:30 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:41:30 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:41:31 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:41:31 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:41:31 - main - INFO - main:102 - Application created successfully
2025-06-09 01:41:31 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:41:31 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:41:31 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:42:43 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:42:43 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:42:43 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:42:43 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:42:43 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:42:43 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:42:43 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:42:43 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:42:43 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_read_excel:126 - Successfully read Excel file (cooperative): C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _load_global_data:667 - Loaded sheet 'Suivi Tickets' with 6 rows
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_read_excel:126 - Successfully read Excel file (cooperative): C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _load_global_data:667 - Loaded sheet 'Traitement CMS Adr' with 89 rows
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_read_excel:126 - Successfully read Excel file (cooperative): C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _load_global_data:667 - Loaded sheet 'Traitement PA' with 496 rows
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1043 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1044 - DataFrame shape: (6, 18)
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1065 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1066 - Has 'Date Livraison' column: True
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1125 - Communes traitées ce mois: 6
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1126 - Communes autres statuts: {}
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1031 - Team KPIs calculated - DMT: 252.7min (from 3 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:870 - Analyzed statistics for 3 collaborators
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _update_overview_display:1188 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _update_overview_display:1189 - Communes traitées mois courant: 6
2025-06-09 01:42:44 - ui.modules.team_stats_module - INFO - _update_overview_display:1190 - Communes autres statuts: {}
2025-06-09 01:42:45 - ui.modules.team_stats_module - INFO - _update_export_filters:2915 - Export filters updated with 4 collaborators (including 'Toute l'équipe')
2025-06-09 01:42:45 - ui.modules.team_stats_module - INFO - _load_global_data:703 - Global data loaded and analyzed successfully
2025-06-09 01:42:50 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1592 - Dependencies verified successfully
2025-06-09 01:42:50 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1609 - Starting CTJ export for BACHOUEL IHEB, Juin 2025
2025-06-09 01:42:50 - ui.modules.team_stats_module - INFO - _calculate_monthly_ctj:1688 - Individual export: Found 139 rows for BACHOUEL IHEB
2025-06-09 01:42:50 - ui.modules.team_stats_module - INFO - _export_ctj_to_excel:1632 - CTJ data calculated: 30 records
2025-06-09 01:42:54 - ui.modules.team_stats_module - INFO - _create_individual_export:2106 - Creating individual export for BACHOUEL IHEB, data length: 30
2025-06-09 01:42:54 - ui.modules.team_stats_module - INFO - _add_individual_summary:2315 - Added individual summary for BACHOUEL IHEB: Total CTJ=139, Working days=3
2025-06-09 01:42:54 - ui.modules.team_stats_module - INFO - _create_individual_export:2211 - Individual export: wrote 139 total CTJ values for BACHOUEL IHEB
2025-06-09 01:42:54 - ui.modules.team_stats_module - INFO - _create_individual_export:2235 - Individual export sheet created successfully
2025-06-09 01:42:54 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:1902 - Default sheet removed successfully
2025-06-09 01:42:54 - ui.modules.team_stats_module - INFO - _create_horizontal_ctj_excel:1914 - Streamlined CTJ Excel file created successfully: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_BACHOUEL_IHEB_20250609.xlsx
2025-06-09 01:42:55 - ui.modules.team_stats_module - INFO - _create_ctj_excel_file:1874 - CTJ Excel file created: C:/Users/<USER>/OneDrive - orange.com/Bureau/Stat_BACHOUEL_IHEB_20250609.xlsx
2025-06-09 01:42:56 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:42:56 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:43:05 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:43:05 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:43:06 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:43:06 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:43:07 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:43:07 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:43:07 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:43:07 - ui.modules.suivi_global_module - INFO - __init__:52 - Core components initialized
2025-06-09 01:43:07 - ui.modules.suivi_global_module - INFO - _create_module_ui:122 - Module UI created successfully
2025-06-09 01:43:07 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:43:07 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:43:07 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:43:07 - ui.modules.suivi_global_module - INFO - _restore_session:1988 - Session restored successfully
2025-06-09 01:43:07 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:108 - Optional features initialized successfully
2025-06-09 01:43:08 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:08 - utils.excel_concurrency - INFO - safe_read_excel:126 - Successfully read Excel file (cooperative): C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:08 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _load_existing_communes:560 - Loaded 6 existing communes for comparison
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ARIES ESPENAN
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ESPLAS
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: MONTCHALONS
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SELIGNE
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SURDOUX
2025-06-09 01:43:08 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:582 - Analysis: 0 new communes, 6 communes to update
2025-06-09 01:43:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:43:15 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:43:15 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:43:16 - ui.home_screen - INFO - _open_suivi_generator:327 - User clicked Suivi Generator button
2025-06-09 01:43:16 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_generator
2025-06-09 01:43:16 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_generator (Générateur Suivi)
2025-06-09 01:43:16 - ui.modules.suivi_generator_module - INFO - __init__:54 - Core components initialized
2025-06-09 01:43:16 - ui.modules.suivi_generator_module - INFO - _create_module_ui:117 - Module UI created successfully
2025-06-09 01:43:16 - ui.navigation - INFO - _load_module:297 - Module suivi_generator created and loaded successfully
2025-06-09 01:43:16 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_generator
2025-06-09 01:43:16 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:43:16 - ui.components.project_info - INFO - update_commune_field:147 - Commune field updated: TUNIS
2025-06-09 01:43:16 - ui.components.project_info - INFO - update_insee_field:161 - INSEE field updated: 07393
2025-06-09 01:43:16 - ui.modules.suivi_generator_module - INFO - _restore_session:482 - Session restored successfully
2025-06-09 01:43:16 - ui.modules.suivi_generator_module - INFO - _initialize_optional_features:102 - Optional features initialized successfully
2025-06-09 01:43:19 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:43:19 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:43:20 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:43:20 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:43:20 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:43:20 - ui.modules.team_stats_module - INFO - cleanup:1580 - Team Stats module cleaned up
2025-06-09 01:43:20 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:43:20 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:43:20 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:43:20 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:43:20 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:43:20 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:43:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:43:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:43:36 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:36 - utils.excel_concurrency - WARNING - safe_read_excel:130 - File temporarily locked, retrying read: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:37 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:37 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Could not load sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:43:37 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:37 - utils.excel_concurrency - WARNING - safe_read_excel:130 - File temporarily locked, retrying read: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:38 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:38 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Could not load sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:43:38 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:38 - utils.excel_concurrency - WARNING - safe_read_excel:130 - File temporarily locked, retrying read: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:39 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:39 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Could not load sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:43:39 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:870 - Analyzed statistics for 0 collaborators
2025-06-09 01:43:39 - ui.modules.team_stats_module - INFO - _update_overview_display:1188 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:43:39 - ui.modules.team_stats_module - INFO - _update_overview_display:1189 - Communes traitées mois courant: 0
2025-06-09 01:43:39 - ui.modules.team_stats_module - INFO - _update_overview_display:1190 - Communes autres statuts: {}
2025-06-09 01:43:39 - ui.modules.team_stats_module - INFO - _load_global_data:703 - Global data loaded and analyzed successfully
2025-06-09 01:43:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:43:44 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:43:44 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:43:45 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:43:45 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:43:45 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:43:45 - ui.modules.suivi_global_module - INFO - __init__:52 - Core components initialized
2025-06-09 01:43:45 - ui.modules.suivi_global_module - INFO - _create_module_ui:122 - Module UI created successfully
2025-06-09 01:43:45 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:43:45 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:43:45 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:43:45 - ui.modules.suivi_global_module - INFO - _restore_session:1988 - Session restored successfully
2025-06-09 01:43:45 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:108 - Optional features initialized successfully
2025-06-09 01:43:46 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:46 - utils.excel_concurrency - WARNING - safe_read_excel:130 - File temporarily locked, retrying read: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:47 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:47 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:545 - Error reading global file: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:43:47 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ARIES ESPENAN
2025-06-09 01:43:47 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ESPLAS
2025-06-09 01:43:47 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: MONTCHALONS
2025-06-09 01:43:47 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:43:47 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SELIGNE
2025-06-09 01:43:47 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SURDOUX
2025-06-09 01:43:47 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:582 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:43:48 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:958 - Updating existing global Excel file with concurrency control
2025-06-09 01:43:48 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:48 - utils.excel_concurrency - WARNING - safe_read_excel:130 - File temporarily locked, retrying read: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:49 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:50 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:987 - Could not read existing Suivi Tickets: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:43:50 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:50 - utils.excel_concurrency - WARNING - safe_read_excel:130 - File temporarily locked, retrying read: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:43:51 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:51 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:987 - Could not read existing Traitement CMS Adr: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:43:51 - utils.excel_concurrency - INFO - safe_excel_operation:88 - Starting Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:51 - utils.excel_concurrency - WARNING - safe_read_excel:130 - File temporarily locked, retrying read: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:52 - utils.excel_concurrency - INFO - safe_excel_operation:100 - Completed Excel operation: read on C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:43:52 - ui.modules.suivi_global_module - WARNING - read_existing_sheets:987 - Could not read existing Traitement PA: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:43:52 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1050 - Unexpected error updating global file: name 'page1_df' is not defined
2025-06-09 01:43:52 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1060 - Error creating/updating global Excel file: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:43:52 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:43:52 - ui.modules.suivi_global_module - ERROR - on_error:889 - Error updating Excel: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:43:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:44:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:45:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:46:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:47:52 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:02 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:12 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:22 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:32 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:48:42 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:49:01 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:49:01 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:49:01 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:49:01 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:49:01 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:49:01 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:49:01 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:49:01 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:49:01 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:49:01 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:49:01 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:49:01 - main - INFO - main:99 - Creating application...
2025-06-09 01:49:03 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:49:03 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:49:03 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:49:03 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:49:03 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:49:03 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:49:03 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:49:03 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:49:04 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:49:04 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:49:04 - main - INFO - main:102 - Application created successfully
2025-06-09 01:49:04 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:49:04 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:49:04 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:49:09 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:49:09 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:49:09 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:49:09 - ui.modules.team_stats_module - INFO - __init__:50 - Core components initialized
2025-06-09 01:49:09 - ui.modules.team_stats_module - INFO - _create_module_ui:131 - Module UI created successfully
2025-06-09 01:49:09 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:49:09 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:49:09 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:49:09 - ui.modules.team_stats_module - INFO - _initialize_optional_features:117 - Optional features initialized successfully
2025-06-09 01:49:10 - utils.simple_excel_access - INFO - simple_read_excel:42 - File temporarily busy, retrying...
2025-06-09 01:49:11 - utils.simple_excel_access - ERROR - simple_read_excel:52 - Unexpected error in simple_read_excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:49:11 - ui.modules.team_stats_module - WARNING - _load_global_data:670 - Could not load sheet 'Suivi Tickets' - using empty DataFrame
2025-06-09 01:49:11 - utils.simple_excel_access - INFO - simple_read_excel:42 - File temporarily busy, retrying...
2025-06-09 01:49:12 - utils.simple_excel_access - ERROR - simple_read_excel:52 - Unexpected error in simple_read_excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:49:12 - ui.modules.team_stats_module - WARNING - _load_global_data:670 - Could not load sheet 'Traitement CMS Adr' - using empty DataFrame
2025-06-09 01:49:12 - utils.simple_excel_access - INFO - simple_read_excel:42 - File temporarily busy, retrying...
2025-06-09 01:49:13 - utils.simple_excel_access - ERROR - simple_read_excel:52 - Unexpected error in simple_read_excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:49:13 - ui.modules.team_stats_module - WARNING - _load_global_data:670 - Could not load sheet 'Traitement PA' - using empty DataFrame
2025-06-09 01:49:13 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:865 - Analyzed statistics for 0 collaborators
2025-06-09 01:49:13 - ui.modules.team_stats_module - INFO - _update_overview_display:1183 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:49:13 - ui.modules.team_stats_module - INFO - _update_overview_display:1184 - Communes traitées mois courant: 0
2025-06-09 01:49:13 - ui.modules.team_stats_module - INFO - _update_overview_display:1185 - Communes autres statuts: {}
2025-06-09 01:49:13 - ui.modules.team_stats_module - INFO - _load_global_data:698 - Global data loaded and analyzed successfully
2025-06-09 01:49:17 - utils.simple_excel_access - INFO - simple_read_excel:42 - File temporarily busy, retrying...
2025-06-09 01:49:18 - utils.simple_excel_access - ERROR - simple_read_excel:52 - Unexpected error in simple_read_excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:49:18 - ui.modules.team_stats_module - WARNING - _load_global_data:670 - Could not load sheet 'Suivi Tickets' - using empty DataFrame
2025-06-09 01:49:18 - utils.simple_excel_access - INFO - simple_read_excel:42 - File temporarily busy, retrying...
2025-06-09 01:49:19 - utils.simple_excel_access - ERROR - simple_read_excel:52 - Unexpected error in simple_read_excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:49:19 - ui.modules.team_stats_module - WARNING - _load_global_data:670 - Could not load sheet 'Traitement CMS Adr' - using empty DataFrame
2025-06-09 01:49:19 - utils.simple_excel_access - INFO - simple_read_excel:42 - File temporarily busy, retrying...
2025-06-09 01:49:20 - utils.simple_excel_access - ERROR - simple_read_excel:52 - Unexpected error in simple_read_excel: [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:49:20 - ui.modules.team_stats_module - WARNING - _load_global_data:670 - Could not load sheet 'Traitement PA' - using empty DataFrame
2025-06-09 01:49:20 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:865 - Analyzed statistics for 0 collaborators
2025-06-09 01:49:20 - ui.modules.team_stats_module - INFO - _update_overview_display:1183 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:49:20 - ui.modules.team_stats_module - INFO - _update_overview_display:1184 - Communes traitées mois courant: 0
2025-06-09 01:49:20 - ui.modules.team_stats_module - INFO - _update_overview_display:1185 - Communes autres statuts: {}
2025-06-09 01:49:20 - ui.modules.team_stats_module - INFO - _load_global_data:698 - Global data loaded and analyzed successfully
2025-06-09 01:49:20 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:49:20 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:49:22 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:49:22 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:49:22 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:49:22 - ui.modules.suivi_global_module - INFO - __init__:52 - Core components initialized
2025-06-09 01:49:22 - ui.modules.suivi_global_module - INFO - _create_module_ui:122 - Module UI created successfully
2025-06-09 01:49:22 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:49:22 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:49:22 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:49:22 - ui.modules.suivi_global_module - INFO - _restore_session:1988 - Session restored successfully
2025-06-09 01:49:22 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:108 - Optional features initialized successfully
2025-06-09 01:49:24 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:49:24 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:564 - Error loading existing communes: 'NoneType' object has no attribute 'empty'
2025-06-09 01:49:24 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ARIES ESPENAN
2025-06-09 01:49:24 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ESPLAS
2025-06-09 01:49:24 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: MONTCHALONS
2025-06-09 01:49:24 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:49:24 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SELIGNE
2025-06-09 01:49:24 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SURDOUX
2025-06-09 01:49:24 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:582 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:49:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:49:27 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:958 - Updating existing global Excel file with concurrency control
2025-06-09 01:49:29 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:49:30 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:49:32 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:49:32 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1050 - Unexpected error updating global file: name 'page1_df' is not defined
2025-06-09 01:49:32 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1060 - Error creating/updating global Excel file: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:49:32 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:49:32 - ui.modules.suivi_global_module - ERROR - on_error:889 - Error updating Excel: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:49:36 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:49:36 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:49:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:49:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:49:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:50:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:50:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:50:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:50:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:50:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:50:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:51:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:51:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:51:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:51:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:51:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:51:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:52:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:52:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:52:27 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:52:37 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:52:47 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:52:57 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:53:07 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:53:17 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:53:37 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:53:37 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:53:37 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:53:37 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:53:37 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:53:37 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:53:37 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:53:37 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:53:37 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:53:37 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:53:37 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:53:37 - main - INFO - main:99 - Creating application...
2025-06-09 01:53:38 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:53:38 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:53:38 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:53:38 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:53:38 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:53:38 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:53:38 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:53:38 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:53:39 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:53:39 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:53:39 - main - INFO - main:102 - Application created successfully
2025-06-09 01:53:39 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:53:39 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:53:39 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:53:46 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:53:46 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:53:46 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:53:46 - ui.modules.team_stats_module - INFO - __init__:51 - Core components initialized
2025-06-09 01:53:46 - ui.modules.team_stats_module - INFO - _create_module_ui:132 - Module UI created successfully
2025-06-09 01:53:46 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:53:46 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:53:46 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:53:46 - ui.modules.team_stats_module - INFO - _initialize_optional_features:118 - Optional features initialized successfully
2025-06-09 01:53:47 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Suivi Tickets' in 1 second...
2025-06-09 01:53:48 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:53:48 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Traitement CMS Adr' in 1 second...
2025-06-09 01:53:49 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:53:49 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Traitement PA' in 1 second...
2025-06-09 01:53:50 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:53:50 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:870 - Analyzed statistics for 0 collaborators
2025-06-09 01:53:50 - ui.modules.team_stats_module - INFO - _update_overview_display:1188 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:53:50 - ui.modules.team_stats_module - INFO - _update_overview_display:1189 - Communes traitées mois courant: 0
2025-06-09 01:53:50 - ui.modules.team_stats_module - INFO - _update_overview_display:1190 - Communes autres statuts: {}
2025-06-09 01:53:50 - ui.modules.team_stats_module - INFO - _load_global_data:703 - Global data loaded and analyzed successfully
2025-06-09 01:53:54 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:53:54 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:53:54 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:53:54 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:53:54 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:53:54 - ui.modules.suivi_global_module - INFO - __init__:52 - Core components initialized
2025-06-09 01:53:54 - ui.modules.suivi_global_module - INFO - _create_module_ui:122 - Module UI created successfully
2025-06-09 01:53:54 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:53:55 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:53:55 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:53:55 - ui.modules.suivi_global_module - INFO - _restore_session:1988 - Session restored successfully
2025-06-09 01:53:55 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:108 - Optional features initialized successfully
2025-06-09 01:53:57 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:53:57 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:564 - Error loading existing communes: 'NoneType' object has no attribute 'empty'
2025-06-09 01:53:57 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ARIES ESPENAN
2025-06-09 01:53:57 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ESPLAS
2025-06-09 01:53:57 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: MONTCHALONS
2025-06-09 01:53:57 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:53:57 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SELIGNE
2025-06-09 01:53:57 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SURDOUX
2025-06-09 01:53:57 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:582 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:53:58 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:958 - Updating existing global Excel file with concurrency control
2025-06-09 01:53:59 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:54:00 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:54:01 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:54:03 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:54:03 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1050 - Unexpected error updating global file: name 'page1_df' is not defined
2025-06-09 01:54:03 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1060 - Error creating/updating global Excel file: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:54:03 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:54:03 - ui.modules.suivi_global_module - ERROR - on_error:889 - Error updating Excel: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:54:07 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:958 - Updating existing global Excel file with concurrency control
2025-06-09 01:54:08 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:54:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:54:10 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:54:11 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:54:11 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1050 - Unexpected error updating global file: name 'page1_df' is not defined
2025-06-09 01:54:11 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:1060 - Error creating/updating global Excel file: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:54:11 - utils.performance - ERROR - worker:111 - Async task failed: Excel update - Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:54:11 - ui.modules.suivi_global_module - ERROR - on_error:889 - Error updating Excel: Erreur lors de la mise à jour du fichier global:
name 'page1_df' is not defined
2025-06-09 01:54:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:54:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:54:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:54:48 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:54:48 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:54:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:54:50 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:54:50 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:54:50 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:54:50 - ui.modules.team_stats_module - INFO - cleanup:1580 - Team Stats module cleaned up
2025-06-09 01:54:50 - ui.modules.team_stats_module - INFO - __init__:51 - Core components initialized
2025-06-09 01:54:50 - ui.modules.team_stats_module - INFO - _create_module_ui:132 - Module UI created successfully
2025-06-09 01:54:50 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:54:50 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:54:50 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:54:50 - ui.modules.team_stats_module - INFO - _initialize_optional_features:118 - Optional features initialized successfully
2025-06-09 01:54:51 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Suivi Tickets' in 1 second...
2025-06-09 01:54:52 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:54:52 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Traitement CMS Adr' in 1 second...
2025-06-09 01:54:53 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:54:53 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Traitement PA' in 1 second...
2025-06-09 01:54:54 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:54:54 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:870 - Analyzed statistics for 0 collaborators
2025-06-09 01:54:54 - ui.modules.team_stats_module - INFO - _update_overview_display:1188 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:54:54 - ui.modules.team_stats_module - INFO - _update_overview_display:1189 - Communes traitées mois courant: 0
2025-06-09 01:54:54 - ui.modules.team_stats_module - INFO - _update_overview_display:1190 - Communes autres statuts: {}
2025-06-09 01:54:54 - ui.modules.team_stats_module - INFO - _load_global_data:703 - Global data loaded and analyzed successfully
2025-06-09 01:55:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:55:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:55:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:55:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:55:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:55:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:56:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:56:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:56:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:56:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:56:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:56:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:57:00 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:57:10 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:57:20 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:57:30 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:57:40 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:57:50 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2033 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:58:17 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 01:58:17 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 01:58:17 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 01:58:17 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 01:58:17 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 01:58:17 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 01:58:17 - main - INFO - setup_application:45 - ============================================================
2025-06-09 01:58:17 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 01:58:17 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 01:58:17 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 01:58:17 - main - INFO - setup_application:49 - ============================================================
2025-06-09 01:58:17 - main - INFO - main:99 - Creating application...
2025-06-09 01:58:17 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 01:58:17 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 01:58:18 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 01:58:18 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 01:58:18 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 01:58:18 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 01:58:18 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:58:18 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 01:58:18 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:58:18 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 01:58:18 - main - INFO - main:102 - Application created successfully
2025-06-09 01:58:18 - main - INFO - main:103 - Starting main loop...
2025-06-09 01:58:18 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 01:58:18 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
2025-06-09 01:58:22 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:58:22 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:58:22 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:58:22 - ui.modules.team_stats_module - INFO - __init__:51 - Core components initialized
2025-06-09 01:58:22 - ui.modules.team_stats_module - INFO - _create_module_ui:132 - Module UI created successfully
2025-06-09 01:58:22 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:58:22 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:58:22 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:58:22 - ui.modules.team_stats_module - INFO - _initialize_optional_features:118 - Optional features initialized successfully
2025-06-09 01:58:23 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Suivi Tickets' in 1 second...
2025-06-09 01:58:24 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Suivi Tickets': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:58:24 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Traitement CMS Adr' in 1 second...
2025-06-09 01:58:25 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Traitement CMS Adr': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:58:25 - ui.modules.team_stats_module - INFO - _load_global_data:669 - File busy, retrying 'Traitement PA' in 1 second...
2025-06-09 01:58:26 - ui.modules.team_stats_module - WARNING - _load_global_data:679 - Error loading sheet 'Traitement PA': [Errno 13] Permission denied: 'C:\\Users\\<USER>\\orange.com\\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\\Suivis CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adresse_Plan Adressage\\Suivis Global Tickets CMS Adr_PA.xlsx'
2025-06-09 01:58:26 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:870 - Analyzed statistics for 0 collaborators
2025-06-09 01:58:26 - ui.modules.team_stats_module - INFO - _update_overview_display:1188 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:58:26 - ui.modules.team_stats_module - INFO - _update_overview_display:1189 - Communes traitées mois courant: 0
2025-06-09 01:58:26 - ui.modules.team_stats_module - INFO - _update_overview_display:1190 - Communes autres statuts: {}
2025-06-09 01:58:26 - ui.modules.team_stats_module - INFO - _load_global_data:703 - Global data loaded and analyzed successfully
2025-06-09 01:58:28 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:58:28 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:58:29 - ui.home_screen - INFO - _open_suivi_global:342 - User clicked Suivi Global Tickets button
2025-06-09 01:58:29 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: suivi_global
2025-06-09 01:58:29 - ui.navigation - INFO - _load_module:272 - Loading module: suivi_global (Suivi Global Tickets)
2025-06-09 01:58:29 - ui.modules.suivi_global_module - INFO - __init__:52 - Core components initialized
2025-06-09 01:58:29 - ui.modules.suivi_global_module - INFO - _create_module_ui:122 - Module UI created successfully
2025-06-09 01:58:29 - ui.navigation - INFO - _load_module:297 - Module suivi_global created and loaded successfully
2025-06-09 01:58:29 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: suivi_global
2025-06-09 01:58:29 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:58:29 - ui.modules.suivi_global_module - INFO - _restore_session:2213 - Session restored successfully
2025-06-09 01:58:29 - ui.modules.suivi_global_module - INFO - _initialize_optional_features:108 - Optional features initialized successfully
2025-06-09 01:58:32 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:58:32 - ui.modules.suivi_global_module - ERROR - _load_existing_communes:564 - Error loading existing communes: 'NoneType' object has no attribute 'empty'
2025-06-09 01:58:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ARIES ESPENAN
2025-06-09 01:58:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: ESPLAS
2025-06-09 01:58:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: MONTCHALONS
2025-06-09 01:58:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SAINT ANDRE LA COTE
2025-06-09 01:58:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SELIGNE
2025-06-09 01:58:32 - ui.modules.suivi_global_module - INFO - _process_commune_folders:685 - Processed commune: SURDOUX
2025-06-09 01:58:32 - ui.modules.suivi_global_module - INFO - _analyze_commune_changes:582 - Analysis: 6 new communes, 0 communes to update
2025-06-09 01:58:33 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:931 - Updating existing global Excel file with concurrency control
2025-06-09 01:58:34 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:58:34 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2258 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:58:36 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:58:37 - utils.excel_concurrency - ERROR - safe_read_excel_file:452 - Unexpected error in safe_read_excel_file: 📁 Impossible de lire le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx'.

🔒 Le fichier est peut-être en cours de modification.

💡 Essayez de fermer Excel temporairement et relancez l'opération.
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date d'affectation' in Suivi Tickets
2025-06-09 01:58:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date Livraison' in Suivi Tickets
2025-06-09 01:58:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date Dépose Ticket 501/511' in Suivi Tickets
2025-06-09 01:58:37 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Dépose Ticket UPR' in Suivi Tickets
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:37 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:38 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date affectation' in Traitement CMS Adr
2025-06-09 01:58:38 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date traitement' in Traitement CMS Adr
2025-06-09 01:58:38 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date livraison' in Traitement CMS Adr
2025-06-09 01:58:38 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date traitement' in Traitement PA
2025-06-09 01:58:38 - ui.modules.suivi_global_module - ERROR - _create_or_update_global_excel_file:959 - File access error updating global file: 📁 Le fichier 'Suivis Global Tickets CMS Adr_PA.xlsx' est ouvert dans Excel.

🔄 Veuillez fermer Excel et réessayer.

💡 Astuce: Vous pouvez consulter le fichier dans Excel pendant que l'app fonctionne, mais fermez-le avant les mises à jour.
2025-06-09 01:58:44 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2258 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:58:49 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:931 - Updating existing global Excel file with concurrency control
2025-06-09 01:58:49 - utils.excel_concurrency - INFO - safe_read_excel_file:434 - Successfully read Excel file (retry 1): C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:58:49 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _read_existing_sheets_safely:1027 - Read existing Suivi Tickets data: 6 rows
2025-06-09 01:58:50 - utils.excel_concurrency - INFO - safe_read_excel_file:426 - Successfully read Excel file (cooperative): C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _read_existing_sheets_safely:1027 - Read existing Traitement CMS Adr data: 89 rows
2025-06-09 01:58:50 - utils.excel_concurrency - INFO - safe_read_excel_file:426 - Successfully read Excel file (cooperative): C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _read_existing_sheets_safely:1027 - Read existing Traitement PA data: 496 rows
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date d'affectation' in Suivi Tickets
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date Livraison' in Suivi Tickets
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date Dépose Ticket 501/511' in Suivi Tickets
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Dépose Ticket UPR' in Suivi Tickets
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - WARNING - _normalize_date_value:1940 - Could not parse date string 'Non Créé' in column 'Dépose Ticket UPR'
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date affectation' in Traitement CMS Adr
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date traitement' in Traitement CMS Adr
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date livraison' in Traitement CMS Adr
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _validate_and_format_dates_before_writing:1997 - Validating and formatting date column 'Date traitement' in Traitement PA
2025-06-09 01:58:50 - utils.excel_concurrency - INFO - safe_write_excel_file:502 - Created backup: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\backups\Suivis Global Tickets CMS Adr_PA_backup_20250609_015850.xlsx
2025-06-09 01:58:50 - utils.excel_concurrency - INFO - safe_write_excel_file:516 - Successfully wrote Excel file: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _format_sheet:1820 - Formatting applied to sheet: Suivi Tickets
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _format_sheet:1820 - Formatting applied to sheet: Traitement CMS Adr
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _format_sheet:1820 - Formatting applied to sheet: Traitement PA
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _format_global_excel:1757 - Global Excel file formatted successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:58:50 - ui.modules.suivi_global_module - INFO - _create_or_update_global_excel_file:999 - Global Excel file updated successfully: C:\Users\<USER>\orange.com\BOT G2R - CM Adresses et Plan Adressage - CM Adresses et Plan Adressage\Suivis CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adresse_Plan Adressage\Suivis Global Tickets CMS Adr_PA.xlsx
2025-06-09 01:58:54 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2258 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:59:04 - ui.modules.suivi_global_module - ERROR - _check_concurrency_status:2258 - Error checking concurrency status: name 'file_lock_manager' is not defined
2025-06-09 01:59:05 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 01:59:05 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 01:59:07 - ui.home_screen - INFO - _open_team_stats:357 - User clicked Team Statistics button
2025-06-09 01:59:07 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: team_stats
2025-06-09 01:59:07 - ui.navigation - INFO - _load_module:272 - Loading module: team_stats (Statistiques Équipe)
2025-06-09 01:59:07 - ui.modules.team_stats_module - INFO - cleanup:1580 - Team Stats module cleaned up
2025-06-09 01:59:07 - ui.modules.team_stats_module - INFO - __init__:51 - Core components initialized
2025-06-09 01:59:07 - ui.modules.team_stats_module - INFO - _create_module_ui:132 - Module UI created successfully
2025-06-09 01:59:07 - ui.navigation - INFO - _load_module:297 - Module team_stats created and loaded successfully
2025-06-09 01:59:07 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: team_stats
2025-06-09 01:59:07 - ui.keyboard_shortcuts - INFO - _setup_default_shortcuts:49 - Default keyboard shortcuts registered
2025-06-09 01:59:07 - ui.modules.team_stats_module - INFO - _initialize_optional_features:118 - Optional features initialized successfully
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _load_global_data:666 - ✅ Direct read successful for 'Suivi Tickets': 6 rows
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _load_global_data:666 - ✅ Direct read successful for 'Traitement CMS Adr': 89 rows
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _load_global_data:666 - ✅ Direct read successful for 'Traitement PA': 496 rows
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1043 - Available columns in Suivi Tickets: ['Nom Commune', 'Code INSEE', 'ID tâche Plan Adressage', 'Nbr des voies CM', 'Nbr des IMB PA', "Date d'affectation", 'Temps préparation QGis', 'Durée Totale CM', 'Duréé Totale PA', 'Traitement Optimum', 'Durée Finale', 'Date Livraison', 'Etat Ticket PA ', 'ID Tache 501/511', 'Date Dépose Ticket 501/511', 'Dépose Ticket UPR', 'ID tâche UPR', 'Collaborateur']
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1044 - DataFrame shape: (6, 18)
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1065 - Found status column: 'Etat Ticket PA ' (exists: True)
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1066 - Has 'Date Livraison' column: True
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1125 - Communes traitées ce mois: 0
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _analyze_commune_status_by_date:1126 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _calculate_team_kpis:1031 - Team KPIs calculated - DMT: 310.0min (from 1 collaborators with treated communes), CTJ Today: 0 elements
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _analyze_team_statistics:870 - Analyzed statistics for 1 collaborators
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _update_overview_display:1188 - Team statistics keys: ['total_tickets', 'total_cms_records', 'total_pa_records', 'total_duration_cms', 'total_duration_pa', 'total_duration_finale', 'collaborators', 'communes', 'communes_traitees_mois_courant', 'communes_autres_statuts', 'team_dmt', 'team_ctj_today', 'total_elements_today']
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _update_overview_display:1189 - Communes traitées mois courant: 0
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _update_overview_display:1190 - Communes autres statuts: {'En Attente': 3, 'En Cours': 2}
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _update_export_filters:2915 - Export filters updated with 2 collaborators (including 'Toute l'équipe')
2025-06-09 01:59:08 - ui.modules.team_stats_module - INFO - _load_global_data:703 - Global data loaded and analyzed successfully
2025-06-09 02:09:04 - root - INFO - setup_logging:73 - Logging to file: logs\suivi_generator_20250609.log
2025-06-09 02:09:04 - root - INFO - setup_logging:79 - ==================================================
2025-06-09 02:09:04 - root - INFO - setup_logging:80 - Suivi Generator Application Started
2025-06-09 02:09:04 - root - INFO - setup_logging:81 - Log level: INFO
2025-06-09 02:09:04 - root - INFO - setup_logging:82 - File logging: Enabled
2025-06-09 02:09:04 - root - INFO - setup_logging:83 - ==================================================
2025-06-09 02:09:04 - main - INFO - setup_application:45 - ============================================================
2025-06-09 02:09:04 - main - INFO - setup_application:46 - Starting SofreTrack Pro - Solutions de traitement et génération de données
2025-06-09 02:09:04 - main - INFO - setup_application:47 - Version: 2.1
2025-06-09 02:09:04 - main - INFO - setup_application:48 - Author: Equipe BLI
2025-06-09 02:09:04 - main - INFO - setup_application:49 - ============================================================
2025-06-09 02:09:04 - main - INFO - main:99 - Creating application...
2025-06-09 02:09:05 - ui.styles - INFO - setup_styles:42 - UI styles configured successfully
2025-06-09 02:09:05 - ui.main_window - INFO - _setup_window:84 - Main window configured
2025-06-09 02:09:05 - ui.navigation - INFO - _setup_navigation_bar:104 - Sofrecom logo loaded successfully from: C:\Users\<USER>\OneDrive - orange.com\Bureau\Suivi_Plan Adressage\logo_Sofrecom.png
2025-06-09 02:09:05 - ui.navigation - INFO - register_module:193 - Registered module: Générateur Suivi (suivi_generator)
2025-06-09 02:09:05 - ui.navigation - INFO - register_module:193 - Registered module: Suivi Global Tickets (suivi_global)
2025-06-09 02:09:05 - ui.navigation - INFO - register_module:193 - Registered module: Statistiques Équipe (team_stats)
2025-06-09 02:09:05 - ui.navigation - INFO - navigate_to:214 - Attempting to navigate to: home
2025-06-09 02:09:05 - ui.main_window - INFO - _set_window_icon:166 - Window icon set successfully
2025-06-09 02:09:06 - ui.navigation - INFO - navigate_to:249 - Successfully navigated to: home
2025-06-09 02:09:06 - ui.main_window - INFO - _setup_navigation:127 - Navigation system initialized
2025-06-09 02:09:06 - main - INFO - main:102 - Application created successfully
2025-06-09 02:09:06 - main - INFO - main:103 - Starting main loop...
2025-06-09 02:09:06 - ui.main_window - INFO - run:182 - Starting application main loop
2025-06-09 02:09:06 - ui.main_window - INFO - _post_init:144 - Main window initialization complete
